// 基础API配置
const baseConfig = {
  timeout: 60000,
  baseURL: 'https://personal.neu.edu.cn/prize/',
  // baseURL: 'https://portal.neu.edu.cn/mobile-staging/api',
  // baseURL: 'http://neu-portal-production.**************.nip.io/mobile/api',
}

let api = {}

//#ifdef APP-PLUS
api = {
  ...baseConfig,
  header: {
    'Content-Type': 'application/x-www-form-urlencoded',
    'X-App-Version': plus.runtime?.version || '1.0.0',
    'X-Mobile-Device-UUID': plus.device?.uuid || 'unknown'
  }
}
//#endif

//#ifdef H5
api = {
  ...baseConfig,
  header: {
    'Content-Type': 'application/x-www-form-urlencoded'
  }
}
//#endif

//#ifndef APP-PLUS || H5
// 其他平台（小程序等）
api = {
  ...baseConfig,
  header: {
    'Content-Type': 'application/x-www-form-urlencoded'
  }
}
//#endif

export default api



