<template>
	<view class="login-warp" v-if="showForm">
		<!-- 		<web-view src="../../hybrid/html/index.html"></web-view> -->
		<form @submit.prevent="formSubmit">
			<view class="login-title">
				<view class="login-cont">您好，</view>
				<view class="login-cont1">欢迎使用智慧东大！</view>
			</view>
			<view class="logo">
				<image class="logo-img" src="/static/logo_fes.png" @click="showDeviceId()"></image>
			</view>
			<view class="cont">
				<image class="cont-img" :src="
            form.username ? '/static/icon_username.png' : '/static/username.png'
          "></image>
				<input class="input-warp" name="username" placeholder="请输入您的学工号" v-model="form.username" />
				<view style="clear: both"></view>
			</view>
			<view class="cont">
				<image class="cont-img" :src="
            form.password ? '/static/icon_password.png' : '/static/password.png'
          "></image>
				<input class="input-warp" :type="showP ? 'password' : 'text'" name="password" placeholder="请输入密码"
					v-model="form.password" />
				<!-- <image class="cont-img" :src="showP ?'/static/showP.png':'/static/hiddenP.png'" @click="showP = !showP"></image> -->
				<view style="clear: both"></view>
			</view>
			<view class="remember-psw">
				<checkbox-group @change="remember">
					<label>
						<checkbox value="psw" :checked="rememberPsw" />记住账号密码
					</label>
				</checkbox-group>
			</view>
			<view class="btn-submit">
				<button :class="disabledLogin ? 'btn-1' : 'btn'" form-type="submit" :disabled="disabledLogin">
					登 录
				</button>
			</view>
			<!-- <button @click="onCountSet(1)">账号1</button>
			<button @click="onCountSet(2)">账号2</button> -->
		</form>
		<!-- <sms-verification
      ref="smsVerificationRef"
      @verify="onSmsVerify"
    ></sms-verification> -->

		<!-- <view class="other-title">
			<image src="/static/zwdl.png" class="zwdl"></image>
		</view> -->
		<!-- 	<view class="other-warp">
			<image src="/static/login_phone.png" hidden></image>
			<image src="/static/login_WeChat.png" hidden></image>
			<image src="/static/login_zw.png" @click="showfingerprint()"></image>
			<image src="/static/login_face.png" hidden></image>
		</view> -->
		<!-- <u-popup :show="show" mode="center" :round="true">
			<view class="zw_warp">
				<image src="../../static/show_zw.png"></image>
				<view class="zw_title">请验证指纹</view>
				<view class="zw_cont">享受更安全、更快的登录体验</view>
				<view class="zw_btn" @click="close()">取消</view>
			</view>
		</u-popup>
		<u-popup :show="rememberFinger" mode="center" :round="true">
			<view class="zw_warp">
				<image src="../../static/show_zw.png"></image>
				<view class="zw_title">开通指纹登录</view>
				<view class="zw_cont">享受更安全、更快的登录体验</view>
				<view class="zw_btn1">
					<text @click="close()">取消</text>
					<text @click="rememberFinger1()">确认</text>
				</view>
			</view>
		</u-popup>
		<u-popup :show="disabled" mode="center" :round="true">
			<view class="zw_warp">
				<image src="../../static/show_zw.png"></image>
				<view class="zw_title">{{ result }}</view>
				<view class="zw_btn1">
					<text @click="disabled = false">取消</text>
					<text @click="disabled = false">确认</text>
				</view>
			</view>
		</u-popup>
		<privacy :isShow="showPrivacy" @send="send"></privacy> -->
	</view>
</template>

<script>
	import Privacy from "@/pages/login/privacy.vue";
	import {
		login,
		checkVersionInfo,
		sendSmsVerification,
		verifySmsCode,
		getDeviceIdClientIP,
		getLoginType,
		smsSend,
		smsVerify,
		smsLogin,
	} from "@/api/login";
	import {
		request
	} from "@/utils/request";

	// import {
	// 	getNumber,
	// 	nativeSetCookie
	// } from "@/uni_modules/sync-cookie";

	export default {
		name: "login",
		components: {
			privacy: Privacy,
		},
		data() {
			return {
				showP: true,
				showPrivacy: false,
				form: {
					
					// username: '00XW3230',
					// password: 'mA9=vB3.eE',
					// username: '',
					// password: '',
					username: "00XW2215",
					password: "K!mr700aN",
				// 	username: '00XW3231',
				// 	password: 'T$+xm6*$v+YWG',
				},
				result: "",
				disabled: false,
				show: false,
				rememberPsw: true,
				num: 0,
				version: null,
				rememberFinger: false,
				disabledLogin: false,
				showForm: false,
				tgt: "",
				loginType: 1,
			};
		},
		onLoad(options) {
			getDeviceIdClientIP();
			
			//页面加载完成，获取本地存储的用户名及密码
			const userName = uni.getStorageSync("userName");
			const userPsw = uni.getStorageSync("userPsw");
			const rememberPsw = uni.getStorageSync("rememberPsw");
			const isLogin = uni.getStorageSync("isLogin") == 1;
			// gqcz = 1
			this.checkVersion();
			if (rememberPsw === "dl" && isLogin) {
				if (userName && userPsw) {
					this.$data.form.username = userName;
					this.$data.form.password = userPsw;
					if (this.checkCookieIsOuted()) {
						this.formSubmit({
							detail: {
								value: {
									username: userName,
									password: userPsw,
								},
							},
						});
					} else {
						uni.switchTab({
							url: "/pages/index/index",
						});
					}
				} else {}
			} else {
				this.showForm = true;
				// this.checkVersion();
				if (userName) {
					this.$data.form.username = userName;
				}
			}
			const privacy = uni.getStorageSync("privacy");
			if (privacy !== "2") {
				this.$data.showPrivacy = true;
			}
		},
		onShow() {
			// const that = this;
			// setTimeout(() => {
			//   uni.setStorageSync("userName", that.$data.form.username);
			//   uni.setStorageSync("userPsw", that.$data.form.password);
			//   uni.setStorageSync("expiresTime", new Date().getTime());
			//   uni.setStorageSync("isLogin", 1);
			//   const tgt =  "TGT-00XW3230-84168-vQ6lEtneiL9bU6YEOfjiopmdGNQNofJEeQdxrlNuUQc62ktzTT-tpass";
			//   that.onSmsVerify(tgt);
			// }, 5000);
		},
		methods: {
			checkCookieIsOuted() {
				const expiresTime = uni.getStorageSync("expiresTime");
				if (expiresTime) {
					const currentTime = new Date().getTime();
					const timeDifference = currentTime - expiresTime;
					const twoHoursInMilliseconds = 2 * 60 * 60 * 1000;
					// const twoHoursInMilliseconds = 15 * 1000;
					if (timeDifference > twoHoursInMilliseconds) {
						return true;
					}
				}
				return false;
			},
			onCountSet(type) {
				this.$data.form.username = type == 1 ? "00XW3230" : "neutest1";
				this.$data.form.password = type == 1 ? "mA9=vB3.eE" : "31C*gim4U";
			},
			registerPushNotifaication() {
				const platform = uni.getSystemInfoSync().platform;
				// #ifdef APP-PLUS
				if (
					!["ios", "android"].includes(platform) ||
					plus.device.model.includes("Simulator")
				)
					return;
				// #endif
				// #ifndef APP-PLUS
				if (!["ios", "android"].includes(platform))
					return;
				// #endif
				const aliyunPush = uni.requireNativePlugin("Aliyun-Push");
				const userName =
					uni.getStorageSync("userName") || this.$data.form.userName;

				if (platform == "ios") {
					aliyunPush.addAlias({
							alias: userName,
						},
						(result) => {}
					);
					//通知回调
					aliyunPush.setNotificationCallback({}, (result) => {
						console.log("setNotificationCallback", result);
					});
					//用户点击
					aliyunPush.setNotificationResponseCallback({}, (result) => {
						console.log("setNotificationResponseCallback", result);
					});
					//消息
					aliyunPush.setMessageCallback({}, (result) => {
						console.log("setMessageCallback", result);
					});
				} else if (platform == "android") {
					const channel = uni.requireNativePlugin(
						"Aliyun-Push-NotificationChannel"
					);
					const aliyunThirdPush = uni.requireNativePlugin("Aliyun-ThirdPush");
					aliyunThirdPush.registerThirdPush({}, (result) => {
						const data = JSON.stringify(result);
						console.log("receive third push : " + data);
					});
					const channelRes = channel.isNotificationEnabled({
						id: "neu_channel",
					});
					channel.createChannel({
						id: "neu_channel",
						name: "NEU安卓通知",
						desc: "NEU安卓通知通道",
						importance: 3,
					});
					aliyunPush.registerPush({}, (result) => {
						const event = result.event;
						if (event === "registerPush") {
							const userName = uni.getStorageSync("userName");
							aliyunPush.addAlias({
									alias: userName,
								},
								(result) => {
									// this.handleResult('addAlias', result);
								}
							);
							if (result.code === "success") {
								console.log("注册推送 成功 ");
							} else {
								console.log("注册推送 " + result.code + " " + result.msg);
							}
						} else {
							const data = JSON.stringify(result.data);
							console.log("receive push event : " + event);
							console.log("receive push data : " + data);
						}
					});
				}
			},
			send() {
				this.$data.showPrivacy = false;
			},
			loginSuccess() {},
			checkIsOuted() {
				const expiresTime = uni.getStorageSync("expiresTime");
				if (expiresTime) {
					const currentTime = new Date().getTime();
					const timeDifference = currentTime - expiresTime;
					const twoHoursInMilliseconds = 2 * 60 * 60 * 1000;
					// const twoHoursInMilliseconds = 15 * 1000;
					if (timeDifference > twoHoursInMilliseconds) {
						return true;
					}
				}
				return false;
			},
			onSmsVerify: function(_tgt) {
				const that = this;
				const tgt = _tgt; 
				const cookieIsOk = () => {
					uni.setStorageSync("userName", that.$data.form.username);
					uni.setStorageSync("userPsw", that.$data.form.password);
					uni.setStorageSync("expiresTime", new Date().getTime());
					uni.setStorageSync("isLogin", 1);
					if (that.$data.rememberPsw) {
						uni.setStorageSync("rememberPsw", "dl");
					}
					that.$data.disabledLogin = false;
					that.registerPushNotifaication();
					setTimeout(() => {
						// #ifdef APP-PLUS
						plus.nativeUI.closeWaiting();
						// #endif
						// #ifdef H5
						uni.hideLoading();
						// #endif
						uni.switchTab({
							url: "/pages/index/index",
						});
					}, 100);
				};

				if (uni.getSystemInfoSync().platform == "ios") {
					console.log("iOS方法");
					nativeSetCookie({
							name: "CASTGC",
							value: tgt,
							domain: "pass.neu.edu.cn",
							path: "/tpass/",
						},
						(success) => {
							if (success) {
								cookieIsOk();
							}
						}
					);
				} else {
					// #ifdef APP-PLUS
					plus.navigator.setCookie(
						"https://pass.neu.edu.cn",
						`CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/;`
					);
					// #endif
					// #ifdef H5
					// H5环境下设置cookie
					document.cookie = `CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/; domain=.neu.edu.cn`;
					// #endif
					cookieIsOk();
				}
			},
			formSubmit: async function(e) {
				// 在H5环境下，直接使用Vue的数据绑定，而不是表单事件数据
				let data = {};
				// #ifdef H5
				data = {
					username: this.form.username,
					password: this.form.password,
					rememberPsw: this.rememberPsw ? 'dl' : 'kb'
				};
				// #endif
				// #ifndef H5
				data = e.detail.value;
				// #endif

				let that = this;
				console.log('登录数据:', data); // 调试用

				if (data.username) {
					if (data.password) {
						this.$data.disabledLogin = true;
						// #ifdef APP-PLUS
						plus.nativeUI.showWaiting();
						// #endif
						// #ifdef H5
						uni.showLoading({
							title: '登录中...',
							mask: true
						});
						// #endif
						const {
							code,
							result,
							msg
						} = await smsLogin(data);
						if (code == 1) {
							that.$data.tgt = result;
							that.onSmsVerify(result);
						} else if (code == 3) {
							const path =
								`tempUUID=${result}&username=${data.username}&password=${encodeURIComponent(data.password)}&rememberPsw=${data.rememberPsw}`;
							uni.reLaunch({
								url: `/pages/smsVerify/index?${path}`,
							});
							// that.$refs.smsVerificationRef.open();
							// that.$refs.smsVerificationRef.tempUUID = result;
							// #ifdef APP-PLUS
							plus.nativeUI.closeWaiting();
							// #endif
							// #ifdef H5
							uni.hideLoading();
							// #endif
						} else if (code == 2 || code == 4) {
							uni.showToast({
								title: msg,
								icon: "error",
								duration: 2000,
							});
						} else {
							// #ifdef APP-PLUS
							plus.nativeUI.closeWaiting();
							// #endif
							// #ifdef H5
							uni.hideLoading();
							// #endif
							uni.showToast({
								title: "身份认证失败",
								icon: "error",
								duration: 2000,
							});
							that.$data.disabledLogin = false;
						}
						return;

						//   login(data)
						//     .then((res) => {
						//       if (res.code == 1) {
						//         that.$data.tgt = res.result.tgt;
						//         getLoginType().then((loginTypeRes) => {
						//           console.log("getLoginType", loginTypeRes.result);
						//           if (loginTypeRes.result === "sms") {
						//             that.$refs.smsVerificationRef.open();
						//             plus.nativeUI.closeWaiting();
						//           } else {
						//             that.onSmsVerify();
						//           }
						//         });
						//       } else {
						//         plus.nativeUI.closeWaiting();
						//         uni.showToast({
						//           title: "身份认证失败",
						//           icon: "error",
						//           duration: 2000,
						//         });
						//         that.$data.disabledLogin = false;
						//       }
						//     })
						//     .catch((res) => {
						//       uni.showToast({
						//         title: "请求失败",
						//         icon: "error",
						//         duration: 2000,
						//       });
						//       that.$data.disabledLogin = false;
						//     });
					} else {
						uni.showModal({
							content: "请输入密码",
						});
					}
				} else {
					uni.showModal({
						content: "请输入您的学工号",
					});
				}
			},
			showfingerprint() {
				// #ifdef APP-PLUS
				if (!plus.fingerprint.isSupport()) {
					this.$data.result = "此设备不支持指纹识别";
					this.$data.disabled = true;
					return;
				} else if (!plus.fingerprint.isKeyguardSecure()) {
					this.$data.result = "此设备未设置密码锁屏，无法使用指纹识别";
					this.$data.disabled = true;
					return;
				} else if (!plus.fingerprint.isEnrolledFingerprints()) {
					this.$data.result = "此设备未录入指纹，请到设置中开启";
					this.$data.disabled = true;
					return;
				} else {
					this.$data.result = "此设备支持指纹识别";
					this.$data.disabled = false;
				}
				// #endif
				// #ifdef MP-WEIXIN
				this.$data.disabled = false;
				this.$data.result = "请在微信真机中使用，模拟器不支持";
				// #endif
				// #ifndef APP-PLUS || MP-WEIXIN
				this.$data.result = "此平台不支持指纹识别";
				// #endif
				if (uni.getStorageSync("fingerprint")) {
					if (!this.$data.form.password) {
						uni.showModal({
							content: "请输入密码",
						});
						return;
					}
					this.$data.show = true;
					this.fingerprint();
				} else {
					this.$data.rememberFinger = true;
				}
			},
			rememberFinger1() {
				if (this.$data.form.username) {
					if (this.$data.form.password) {
						uni.setStorageSync("fingerprint", true);
						this.fingerprint();
					} else {
						uni.showModal({
							content: "请输入密码",
						});
						this.$data.rememberFinger = false;
					}
				} else {
					uni.showModal({
						content: "请输入您的学工号",
					});
					this.$data.rememberFinger = false;
				}
			},
			fingerprint: function() {
				// let that = this;
				// // #ifdef APP-PLUS
				// plus.fingerprint.authenticate(
				// 	function() {
				// 		plus.nativeUI.closeWaiting(); //兼容Android平台关闭等待框
				// 		that.$data.show = false;
				// 		that.$data.result = "指纹识别成功";
				// 		let data = {
				// 			username: uni.getStorageSync("userName"),
				// 			password: uni.getStorageSync("userPsw"),
				// 		};
				// 		login(data)
				// 			.then((res) => {
				// 				if (res.code == 1) {
				// 					that.registerPushNotifaication();
				// 					const tgt = res.result.tgt;
				// 					uni.setStorageSync("tgt", tgt);
				// 					// console.log(tgt)
				// 					plus.navigator.setCookie(
				// 						"https://pass.neu.edu.cn/",
				// 						"CASTGC=" +
				// 						tgt +
				// 						"; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/"
				// 					);

				// 					uni.setStorageSync("expiresTime", new Date().getTime());
				// 					uni.switchTab({
				// 						url: "/pages/index/index",
				// 					});
				// 					that.$data.disabledLogin = false;
				// 				} else {
				// 					uni.showToast({
				// 						title: "身份认证失败",
				// 						icon: "error",
				// 						duration: 2000,
				// 					});
				// 					that.$data.disabledLogin = false;
				// 				}
				// 			})
				// 			.catch(() => {
				// 				uni.showToast({
				// 					title: "请求失败",
				// 					icon: "error",
				// 					duration: 2000,
				// 				});
				// 			});
				// 	},
				// 	function(e) {
				// 		switch (e.code) {
				// 			case e.AUTHENTICATE_MISMATCH:
				// 				plus.nativeUI.toast("指纹匹配失败，请重新输入");
				// 				break;
				// 			case e.AUTHENTICATE_OVERLIMIT:
				// 				plus.nativeUI.closeWaiting(); //兼容Android平台关闭等待框
				// 				plus.nativeUI.alert(
				// 					"指纹识别失败次数超出限制，请使用其它方式进行认证"
				// 				);
				// 				break;
				// 			case e.CANCEL:
				// 				plus.nativeUI.toast("已取消识别");
				// 				break;
				// 			default:
				// 				plus.nativeUI.closeWaiting(); //兼容Android平台关闭等待框
				// 				plus.nativeUI.alert("指纹识别失败，请重试");
				// 				break;
				// 		}
				// 		// Android平台手动弹出等待提示框
				// 		if ("Android" == plus.os.name) {
				// 			this.$data.show = true;
				// 		}
				// 		// #endif

				// 		// #ifdef MP-WEIXIN
				// 		wx.startSoterAuthentication({
				// 			requestAuthModes: ["fingerPrint"],
				// 			challenge: "123456",
				// 			authContent: "请用指纹解锁",
				// 			success() {
				// 				uni.showToast({
				// 					title: "识别成功",
				// 					mask: false,
				// 					duration: 1500,
				// 				});
				// 			},
				// 		});
				// 		// #endif
				// 	}
				// )
			},
			remember() {
				this.$data.rememberPsw = !this.$data.rememberPsw;
				if (this.$data.rememberPsw) {
					uni.setStorageSync("rememberPsw", "dl");
				} else {
					uni.setStorageSync("rememberPsw", "kb");
				}
			},
			async checkVersion() {
				const _this = this;
				// #ifdef APP-PLUS
				plus.runtime.getProperty(plus.runtime.appid, (appInfo) => {
					// 获取版本号
					const currentVersion = appInfo.version;
					// 这是用来看看版本是多少的
					_this.getVersionFn(currentVersion);
				});
				// #endif
				// #ifdef H5
				// H5环境下跳过版本检查
				console.log('H5环境，跳过版本检查');
				// #endif
			},
			compareVersion(v1, v2) {
				const version1 = v1.split(".").map(Number);
				const version2 = v2.split(".").map(Number);

				for (let i = 0; i < version1.length; i++) {
					if (version1[i] < version2[i]) {
						return -1;
					} else if (version1[i] > version2[i]) {
						return 1;
					}
				}
				return 0;
			},
			async getVersionFn(currentVersion) {
				const systemInfo = uni.getSystemInfoSync();
				// 0 是安卓  1 是iOS
				const os = systemInfo.platform == "android" ? "0" : "1";
				const res = await checkVersionInfo({
					os: os,
				});
				//非强制更新且用户点击过取消
				const cancleClick = uni.getStorageSync("updateCancle");
				if (res.result.is_update == 0 && cancleClick == 1) {
					return;
				}
				const updateUrl = res.result.link; // 下载地址
				const latestVersion = res.result.number; // 版本号
				console.log(
					"线上版本：",
					latestVersion,
					this.compareVersion(currentVersion, latestVersion)
				);
				// 比较版本号，判断是否有新版本
				if (this.compareVersion(currentVersion, latestVersion) < 0) {
					// 提示用户更新
					uni.showModal({
						title: "版本更新",
						content: res.result.desc,
						showCancel: res.result.is_update != 1,
						cancelText: "暂不更新",
						confirmText: "立即更新",
						success: (modalRes) => {
							if (modalRes.confirm) {
								if (os == 0) {
									// 用户确认更新，下载新版本
									this.downloadAndInstall(updateUrl);
								} else {
									let appleId = 1454919505;
									plus.runtime.launchApplication({
											action: `itms-apps://itunes.apple.com/cn/app/id${appleId}?mt=8`,
										},
										function(e) {
											console.log(
												"Open system default browser failed: " + e.message
											);
										}
									);
								}
							} else if (modalRes.cancel) {
								uni.setStorageSync("updateCancle", 1);
							}
						},
					});
				} else {
					console.log("当前已是最新版本");
				}
			},
			downloadAndInstall(url) {
				var dtask = plus.downloader.createDownload(url, {}, function(d, status) {
					// 下载完成
					if (status == 200) {
						plus.runtime.install(
							plus.io.convertLocalFileSystemURL(d.filename), {}, {},
							function(error) {
								uni.showToast({
									title: "安装失败",
									duration: 1500,
								});
							}
						);
					} else {
						uni.showToast({
							title: "更新失败",
							duration: 1500,
						});
					}
				});
				try {
					dtask.start(); // 开启下载的任务
					var prg = 0;
					var showLoading = plus.nativeUI.showWaiting("正在下载"); //创建一个showWaiting对象
					dtask.addEventListener("statechanged", function(task, status) {
						// 给下载任务设置一个监听 并根据状态  做操作
						switch (task.state) {
							case 1:
								showLoading.setTitle("正在下载");
								break;
							case 2:
								showLoading.setTitle("已连接到服务器");
								break;
							case 3:
								prg = parseInt(
									(parseFloat(task.downloadedSize) / parseFloat(task.totalSize)) *
									100
								);
								showLoading.setTitle("  正在下载" + prg + "%  ");
								break;
							case 4:
								plus.nativeUI.closeWaiting();
								//下载完成
								break;
						}
					});
				} catch (err) {
					plus.nativeUI.closeWaiting();
					uni.showToast({
						title: "更新失败",
						mask: false,
						duration: 1500,
					});
				}
			},
			showDeviceId() {
				this.$data.num++;
				if (this.$data.num === 3) {
					// #ifdef APP-PLUS
					uni.showModal({
						content: plus.runtime.version,
					});
					// #endif
					// #ifdef H5
					uni.showModal({
						content: 'H5版本',
					});
					// #endif
					this.$data.num = 0;
				}
			},
			close() {
				this.$data.show = false;
				this.$data.rememberFinger = false;
				// #ifdef APP-PLUS
				plus.fingerprint.cancel();
				// #endif
			},
		},
	};
</script>

<style lang="scss" scoped>
	.login-warp {
		background-image: url("../../static/login_bgfes.png");
		background-size: 100vw 518rpx;
		background-repeat: no-repeat;
		background-color: #fff;
		padding-top: 140rpx;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		overflow-y: auto;
	}

	.opi0 {
		opacity: 0;
	}

	.login-warp .login-title {
		padding-left: 80rpx;
		padding-bottom: 120rpx;
		color: #fff;
	}

	.login-warp .login-title .login-cont {
		font-size: 52rpx;
		font-weight: bold;
	}

	.login-warp .login-title .login-cont1 {
		font-size: 42rpx;
		margin-top: 10rpx;
	}

	.cont {
		width: 560rpx;
		height: 70rpx;
		margin: 0 auto;
		border-radius: 50rpx;
		background-color: #f3f3f3;
		padding-left: 40rpx;
		padding-top: 30rpx;
		margin-bottom: 40rpx;
		overflow: hidden;
	}

	.cont .cont-img {
		float: left;
		width: 49rpx;
		height: 49rpx;
		margin-right: 20rpx;
		margin-top: -5rpx;
	}

	.cont .input-warp {
		float: left;
		width: 415rpx;
	}

	.cont .form-title {
		font-size: 30rpx;
		margin-left: 6rpx;
		margin-bottom: 10rpx;
		padding-left: 20rpx;
		width: 100rpx;
		height: 80rpx;
		line-height: 80rpx;
	}

	.cont .form-title text {
		color: red;
	}

	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo {
		height: 280rpx;
		width: 28s0rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 32rpx;
		text-align: center;
	}

	.logo .logo-img {
		height: 280rpx;
		width: 280rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #f3f3f3;
	}

	.remember-psw {
		margin-left: 120rpx;
	}

	.btn-submit {
		width: 600rpx;
		height: 100rpx;
		border-radius: 50rpx;
		border: 0 none;
		margin: 40rpx auto 0;
		overflow: hidden;
	}

	.btn-submit .btn {
		border: 0 none;
		background: none;
		color: #fff;
		background: linear-gradient(to bottom, #63c6ff, #40acff);
		width: 606rpx;
		height: 106rpx;
		margin-top: -2rpx;
		margin-left: -2rpx;
		line-height: 106rpx;
	}

	.btn-submit .btn-1 {
		border: 0 none;
		background: none;
		color: #fff;
		background: linear-gradient(to bottom, #f5f5f5, #b2b2b2);
		width: 606rpx;
		height: 106rpx;
		margin-top: -2rpx;
		margin-left: -2rpx;
		line-height: 106rpx;
	}

	.other-title {
		margin-top: 40rpx;
		text-align: center;
	}

	// .other-title image.other {
	// 	width: 410rpx;
	// 	height: 36rpx;
	// }
	.other-title image.zwdl {
		width: 346rpx;
		height: 28rpx;
	}

	.other-warp {
		width: 70rpx;
		margin: 40rpx auto 0;
	}

	.other-warp image {
		width: 70rpx;
		height: 70rpx;
	}

	.zw_warp {
		text-align: center;
		width: 88vw;
		padding-top: 50rpx;
	}

	.zw_warp image {
		width: 100rpx;
		height: 100rpx;
		margin-bottom: 40rpx;
	}

	.zw_warp .zw_title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.zw_warp .zw_cont {
		font-size: 28rpx;
		color: #9094a6;
		margin-bottom: 30rpx;
	}

	.zw_warp .zw_btn,
	.zw_warp .zw_btn1 {
		height: 100rpx;
		line-height: 100rpx;
		color: #40acff;
		font-size: 28rpx;
		font-weight: bold;
		border-top: 2rpx solid #9094a6;
	}

	.zw_warp .zw_btn1 {
		display: flex;
		justify-content: space-between;
	}

	.zw_warp .zw_btn1 text {
		flex: 1;
	}

	.zw_warp .zw_btn1 text:first-child {
		color: #9094a6;
	}
</style>