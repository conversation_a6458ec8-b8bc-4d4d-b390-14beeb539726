<template>
	<view class="sms-verification">
		<view class="content">
			<view class="input-container">
				<input v-model="smsCode" type="number" placeholder="请输入短信验证码" maxlength="6" class="sms-input" />
				<button class="send-btn" :disabled="countdown > 0" @click="sendSmsCode">
					{{ countdown > 0 ? `${countdown}s后重试` : "获取验证码" }}
				</button>
			</view>

			<view class="trust-device-row">
				<checkbox-group @change="onCheckBoxChange">
					<label>
						<checkbox value="trustDevice" checked="true" class="trust-checkbox" color="#3553A2" />是否信任该设备
					</label>
				</checkbox-group>
				<!-- 				<checkbox v-model="trustDevice" class="trust-checkbox" color="#3553A2" /> -->
				<!-- 				<text class="trust-label">是否信任该设备</text> -->
			</view>

			<button class="verify-btn" :disabled="!isValid" @click="handleVerify">
				验证
			</button>
		</view>

	</view>
</template>

<script>
	// import {
	// 	getNumber,
	// 	nativeSetCookie
	// } from "@/uni_modules/sync-cookie";
	import {
		smsSend,
		smsVerify
	} from "@/api/login";
	import {
		registerPushNotifaication
	} from "@/utils/registerNotificatio.js";
	export default {
		name: "SmsVerification",
		data() {
			return {
				smsCode: "",
				countdown: 0,
				timer: null,
				tempUUID: "",
				trustDevice: true,
			};
		},
		computed: {
			isValid() {
				return /^\d{6}$/.test(this.smsCode);
			},
		},
		beforeDestroy() {
			this.clearTimer();
		},
		onLoad(options) {
			
			if (options) {
				this.tempUUID = options.tempUUID || uni.getStorageSync('tempUUID');
				this.username = options.username || uni.getStorageSync('userName') ;
				this.password = options.password || uni.getStorageSync('userPsw');
				this.rememberPsw = options.rememberPsw;
			}else{
				this.username  = uni.getStorageSync("userName");
				this.password = uni.getStorageSync("userPsw");
				this.tempUUID = uni.getStorageSync("tempUUID");
			}
			uni.setStorageSync('isSms',1)
		},
		methods: {
			onCheckBoxChange(v) {

				this.trustDevice = v.detail.value.includes("trustDevice")
			},
			startCountdown() {
				this.countdown = 300;
				this.timer = setInterval(() => {
					if (this.countdown <= 0) {
						this.clearTimer();
						return;
					}
					this.countdown--;
				}, 1000);
			},
			clearTimer() {
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = null;
				}
			},
			async sendSmsCode() {
				try {
					if (this.countdown > 0) return;
					// 这里调用发送短信验证码的API
					const res = await smsSend({
						username: this.username,
						password: this.password,
					});
					if (res.code == 1) {
						console.log("res", res);
						uni.showToast({
							title: "验证码已发送",
							icon: "none",
						});
						this.startCountdown();
						this.tempUUID = res.result;
					}
				} catch (error) {
					console.error("发送验证码失败:", error);
					uni.showToast({
						title: "发送失败，请重试",
						icon: "none",
					});
				}
			},
			onSmsVerify: function(_tgt) {
				const that = this;
				const tgt = _tgt;
				// const tgt = "TGT-00XW3230-84168-vQ6lEtneiL9bU6YEOfjiopmdGNQNofJEeQdxrlNuUQc62ktzTT-tpass";
				const cookieIsOk = () => {
					uni.setStorageSync("userName", this.username);
					uni.setStorageSync("userPsw", this.password);
					uni.setStorageSync("expiresTime", new Date().getTime());
					uni.setStorageSync("isLogin", 1);
					if (this.rememberPsw) {
						uni.setStorageSync("rememberPsw", "dl");
					}
					// 验证成功后删除短信认证中的标记
					uni.removeStorageSync("isSms");
					uni.removeStorageSync("tempUUID");
					registerPushNotifaication();
					setTimeout(() => {
						plus.nativeUI.closeWaiting();
						uni.switchTab({
							url: "/pages/index/index",
						});
					}, 100);
				};

				// #ifdef APP-PLUS
				if (uni.getSystemInfoSync().platform == "ios") {
					// iOS平台使用原生cookie设置（需要导入相应模块）
					// 暂时使用plus.navigator.setCookie作为替代
					plus.navigator.setCookie(
						"https://pass.neu.edu.cn",
						`CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/; domain=.neu.edu.cn`
					);
					cookieIsOk();
				} else {
					plus.navigator.setCookie(
						"https://pass.neu.edu.cn",
						`CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/;`
					);
					cookieIsOk();
				}
				// #endif

				// #ifdef H5
				// H5环境下设置cookie
				document.cookie = `CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/; domain=.neu.edu.cn`;
				cookieIsOk();
				// #endif
			},
			async handleVerify() {
				if (!this.isValid) return;
				const temUUID = uni.getStorageSync("tempUUID");
				const params = {

					sms_code: this.smsCode,
					uuid: this.tempUUID,
					trust_device: this.trustDevice ? 1 : 0,

				}
				console.log("prams__:", params)
				const {
					code,
					result,
					msg
				} = await smsVerify(params);
				if (code == 1) {
					this.onSmsVerify(result);
				} else {
					uni.showToast({
						title: msg,
						icon: "none",
					});
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	.sms-verification {
		width: 100vw;
		min-height: 100vh;
		background-color: #fff;
		box-sizing: border-box;
		padding: 0 40rpx 0 40rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		position: relative;

		.content {
			flex: 1;
			padding-top: 80rpx;

			.input-container {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				.sms-input {
					flex: 1;
					height: 88rpx;
					background-color: #f5f5f5;
					border-radius: 8rpx;
					padding: 0 20rpx;
					font-size: 28rpx;
					margin-right: 20rpx;
				}

				.send-btn {
					width: 200rpx;
					height: 88rpx;
					line-height: 88rpx;
					text-align: center;
					background: #3553A2;
					color: #fff;
					font-size: 26rpx;
					border-radius: 8rpx;
					padding: 0;
					margin: 0;

					&[disabled] {
						background: #ccc;
						color: #fff;
					}
				}
			}

			.verify-btn {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				text-align: center;
				background: #3553A2;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				margin: 0 auto;
				margin-top: 200rpx;
				max-width: 670rpx;
				box-shadow: 0 4rpx 16rpx rgba(53, 83, 162, 0.15);

				&[disabled] {
					background: #ccc;
				}
			}
		}
	}

	.trust-device-row {
		display: flex;
		align-items: center;
	}

	.trust-checkbox {
		transform: scale(0.8);
		margin-right: 8rpx;
	}

	.trust-label {
		font-size: 28rpx;
	}
</style>