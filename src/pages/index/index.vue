<template>
	<view id="webViewBox">
		<web-view :src="webUrl" @message="onMessageListen"></web-view>
	</view>
</template>

<script>
	import {
		login,
		checkVersionInfo,
		smsLogin
	} from "@/api/login";
	// import {
	// 	getNumber,
	// 	nativeSetCookie,
	// 	getNativeWebCookie,
	// } from "@/uni_modules/sync-cookie";
	export default {
		components: {},
		data() {
			return {
				isLoaded: false,
				webUrl: "https://personal.neu.edu.cn/portal/frontend/vspage/project/2",
				wv: null,
				isRequest: false,
			};
		},

		onLoad(options) {},
		onHide() {
			console.log("index onHide");
		},
		onShow() {


			const isIOS = uni.getSystemInfoSync().platform == "ios";
			const _this = this;
			const currentWebview = _this.$scope.$getAppWebview();
			plus.nativeUI.showWaiting();
			const timer = setInterval(function() {
				const wv = currentWebview.children()[0];
				uni.hideKeyboard();
				if (wv) {
					if (isIOS) {
					  wv.setStyle({
					    width: "1px",
					  });
					}
					
					clearInterval(timer);
					if (_this.isLoaded) {
						wv.reload();
						//return
					}
					wv.addEventListener("loaded", function() {
						_this.wv = wv;
						if (wv.getURL().includes("pass.neu.edu.cn")) {
							console.log("进入了,说明Cookie TGT 没有了");
							uni.hideKeyboard();
							if (_this.isRequest) {
								return
							}
							const userName = uni.getStorageSync("userName");
							const userPsw = uni.getStorageSync("userPsw");
							const rememberPsw = uni.getStorageSync("rememberPsw");
							if (userName && userPsw) {
								const data = {
									username: userName,
									password: userPsw,
								};
								_this.isRequest = isIOS ? false : true;
								smsLogin(data).then((res) => {
									plus.nativeUI.closeWaiting();
									if (res.code == 1) {
										_this.onSmsVerify(res.result);
									} else if (res.code == 2 || res.code == 4) {
										uni.showToast({
											title: res.msg,
											icon: "none",
										});
									} else if (res.code == 3) {
										plus.nativeUI.closeWaiting();
										uni.setStorageSync("tempUUID", res.result);
										const path =
											`tempUUID=${res.result}&username=${userName}&password=${encodeURIComponent(userPsw)}&rememberPsw=${rememberPsw}`;
										uni.reLaunch({
											url: `/pages/smsVerify/index?${path}`,
										})
									} else {
										uni.showToast({
											title: "身份认证失败",
											icon: "error",
											duration: 2000,
										});
									}
								});
								//   login(data).then((res) => {
								//     if (res.code == 1) {
								//       const tgt = res.result.tgt;
								//       const cookieIsOk = () => {
								//         setTimeout(() => {
								//           uni.setStorageSync("expiresTime", new Date().getTime());
								//           wv.reload();
								//         }, 500);
								//       };
								//       if (uni.getSystemInfoSync().platform == "ios") {
								//         console.log("iOS方法");
								//         nativeSetCookie(
								//           {
								//             name: "CASTGC",
								//             value: tgt,
								//             domain: "pass.neu.edu.cn",
								//             path: "/tpass/",
								//           },
								//           (success) => {
								//             if (success) {
								//               console.log("种入结果——————：成功");
								//               cookieIsOk();
								//             }
								//             cookieIsOk();
								//           }
								//         );
								//         cookieIsOk();
								//       } else {
								//         plus.navigator.setCookie(
								//           "https://pass.neu.edu.cn",
								//           `CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/;`
								//         );
								//         cookieIsOk();
								//       }
								//     }
								//   });
							} else {
								plus.nativeUI.closeWaiting();
								uni.reLaunch({
									url: "/pages/login/login",
								});
							}
						} else {
							wv.setStyle({
								width: "100vw",
							});
							_this.isLoaded = true;
							setTimeout(() => {
								plus.nativeUI.closeWaiting();
							}, 1000);
						}
					});
				}
			}, 10);
		},
		methods: {
			onSmsVerify(_tgt) {
				const tgt = _tgt;
				const _this = this;
				const cookieIsOk = () => {
					setTimeout(() => {
						uni.setStorageSync("expiresTime", new Date().getTime());
						_this.wv.reload();
					}, 500);
				};
				if (uni.getSystemInfoSync().platform == "ios") {
					nativeSetCookie({
							name: "CASTGC",
							value: tgt,
							domain: "pass.neu.edu.cn",
							path: "/tpass/",
						},
						(success) => {
							if (success) {
								cookieIsOk();
							}
							cookieIsOk();
						}
					);
					cookieIsOk();
				} else {
					plus.navigator.setCookie(
						"https://pass.neu.edu.cn",
						`CASTGC=${tgt}; expires=Monday,31-Dec-2029 15:59:59 GMT; path=/tpass/;`
					);
					cookieIsOk();
				}
			},
			onMessageListen(e) {
				const type = e.detail.data[0].type;
				const url = e.detail.data[0].url;
				if (type == "scan") {
					this.scan();
				}
			},
			scan() {
				uni.scanCode({
					onlyFromCamera: true,
					scanType: ["qrCode"],
					success: function(res) {
						let result = res.result;
						let src = "http";
						let platform = uni.getSystemInfoSync().platform;
						if (result.startsWith("http") || result.startsWith("https")) {
							if (platform === "ios") {
								setTimeout(() => {
									uni.navigateTo({
										url: "/pages/webview/appwebview?url=" +
											encodeURIComponent(result),
									});
								}, 100);
								return;
							} else {
								uni.navigateTo({
									url: "/pages/webview/appwebview?url=" + encodeURIComponent(result),
								});
							}
						} else {
							uni.showModal({
								content: res.result,
							});
						}
					},
					fail: function(res) {
						console.log("11", res);
					},
				});
			},
		},
	};
</script>

<style>
	/* .coverView{
  position: fixed;
  top: 10rpx;
  right: 0rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: red;
} */
</style>